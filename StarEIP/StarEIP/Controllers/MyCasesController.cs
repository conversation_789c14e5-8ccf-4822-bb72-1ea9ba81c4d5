using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models;
using StarEIP.Models.App;
using StarEIP.Models.Auth;
using StarEIP.Services.Auth;

namespace StarEIP.Controllers
{
    [Route("api/my-cases")]
    [ApiController]
    [Authorize]
    public class MyCasesController : ControllerBase
    {
        private readonly StarEipDbContext starEipDbContext;
        private readonly ILogger<MyCasesController> logger;

        public MyCasesController(StarEipDbContext starEipDbContext, ILogger<MyCasesController> logger)
        {
            this.starEipDbContext = starEipDbContext;
            this.logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                IQueryable<AuthorizationDto> query = GetMyCasesQuery();

                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                starEipDbContext.ChangeTracker.LazyLoadingEnabled = true;
                starEipDbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                loadOptions.PrimaryKey = [nameof(AuthorizationDto.Id)];
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(query, loadOptions));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get my cases");
                throw;
            }
        }

        private IQueryable<AuthorizationDto> GetMyCasesQuery()
        {
            var currentUserId = User.GetUserId();

            var query = starEipDbContext.Authorizations
                .Include(a => a.User)
                .Include(a => a.Child)
                .ThenInclude(c => c.ReferringPhysician)
                .Include(a => a.ScStatusType)
                .Where(a => a.UserId == currentUserId) // Only show cases assigned to current user
                .Select(a => new AuthorizationDto
                {
                    //auth properties
                    Id = a.Id,
                    ChildName = a.Child.FirstName + " " + a.Child.LastName,
                    AuthType = a.AuthType,
                    AuthNumber = a.AuthNumber,
                    StartDate = a.StartDate,
                    EndDate = a.EndDate,
                    Units = a.Units,
                    ChildId = a.ChildId,
                    StatusId = a.StatusId,
                    UserId = a.UserId,
                    UserName = a.User.UserName,
                    UserFullName = a.User.FirstName + " " + a.User.LastName,

                    // SC-specific properties
                    ScStatus = a.ScStatus,
                    ScStatusName = a.ScStatusType != null ? a.ScStatusType.Name : null,
                    StatusLastUpdated = a.StatusLastUpdated,
                    FollowUpDate = a.FollowUpDate,
                    ScStatusLastUpdated = a.ScStatusLastUpdated
                });

            return query;
        }

        [HttpGet("sc-status-types")]
        public async Task<IActionResult> GetScStatusTypes()
        {
            var scStatusTypes = await starEipDbContext.ScStatusTypes
                .OrderBy(s => s.SortOrder)
                .ToListAsync();
            return Ok(scStatusTypes);
        }

        public record ScStatusUpdateRequest(int ScStatus, DateTime? FollowUpDate);

        [HttpPost("{id}/sc-status")]
        public async Task<IActionResult> UpdateScStatus(int id, [FromBody] ScStatusUpdateRequest request)
        {
            var currentUserId = User.GetUserId();

            var authorization = await starEipDbContext.Authorizations
                .Where(a => a.Id == id && a.UserId == currentUserId) // Ensure user can only update their own cases
                .FirstOrDefaultAsync();

            if (authorization == null)
            {
                return NotFound();
            }

            authorization.ScStatus = request.ScStatus;
            authorization.FollowUpDate = request.FollowUpDate;
            authorization.ScStatusLastUpdated = DateTime.Now;
            await starEipDbContext.SaveChangesAsync();

            var updatedAuthorization = await GetMyCasesQuery()
                .FirstOrDefaultAsync(a => a.Id == id);
            return Ok(updatedAuthorization);
        }
    }
}

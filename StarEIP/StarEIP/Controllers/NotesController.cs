using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models;

namespace StarEIP.Controllers;

[Route("api/notes")]
[ApiController]
[Authorize]
public class NotesController : ControllerBase
{
    private readonly StarEipDbContext _dbContext;
    private readonly ILogger<NotesController> _logger;

    public NotesController(StarEipDbContext dbContext, ILogger<NotesController> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    /// <summary>
    /// Get notes for any entity type
    /// </summary>
    [HttpGet("{entityType}/{entityId}")]
    public async Task<IActionResult> GetNotes(string entityType, int entityId)
    {
        try
        {
            var notes = await _dbContext.Notes
                .Where(n => (n.EntityType == entityType && n.EntityId == entityId) ||
                           (entityType == "child" && n.ChildId == entityId && n.EntityType == null))
                .OrderBy(n => n.CreatedOn)
                .Select(n => new
                {
                    n.Id,
                    n.Note,
                    n.CreatedOn,
                    CreatedBy = n.CreatedBy.HasValue ? n.CreatedBy.Value.ToString() : "Admin"
                })
                .ToListAsync();

            return Ok(notes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving notes for entity {EntityType} with ID {EntityId}", entityType, entityId);
            return StatusCode(500, "An error occurred while retrieving notes.");
        }
    }

    /// <summary>
    /// Get notes for a child (legacy endpoint for backward compatibility)
    /// </summary>
    [HttpGet("child/{childId}")]
    public async Task<IActionResult> GetChildNotes(int childId)
    {
        return await GetNotes("child", childId);
    }

    public record NoteRequest(string Note);

    /// <summary>
    /// Add a note to any entity type
    /// </summary>
    [HttpPost("{entityType}/{entityId}")]
    public async Task<IActionResult> AddNote(string entityType, int entityId, [FromBody] NoteRequest noteRequest)
    {
        try
        {
            var note = new Notes
            {
                Note = noteRequest.Note,
                EntityType = entityType,
                EntityId = entityId
            };

            // For backward compatibility, also set ChildId if entityType is 'child'
            if (string.Equals(entityType, "child", StringComparison.OrdinalIgnoreCase))
            {
                note.ChildId = entityId;
            }

            _dbContext.Notes.Add(note);
            await _dbContext.SaveChangesAsync();
            return Ok(note);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error adding note for entity {EntityType} with ID {EntityId}", entityType, entityId);
            return StatusCode(500, "An error occurred while adding note.");
        }
    }

    /// <summary>
    /// Add a note to a child (legacy endpoint for backward compatibility)
    /// </summary>
    [HttpPost("child/{childId}")]
    public async Task<IActionResult> AddChildNote(int childId, [FromBody] NoteRequest noteRequest)
    {
        return await AddNote("child", childId, noteRequest);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteNote(int id)
    {
        try
        {
            var note = await _dbContext.Notes.FindAsync(id);
            if (note == null)
            {
                return NotFound();
            }

            _dbContext.Notes.Remove(note);
            await _dbContext.SaveChangesAsync();
            return NoContent();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error deleting note with ID {NoteId}", id);
            return StatusCode(500, "An error occurred while deleting note.");
        }
    }
}
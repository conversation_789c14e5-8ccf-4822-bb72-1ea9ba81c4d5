"use client"; // This is a client component

import * as React from "react";
import Image from "next/image";
import { useState } from "react";
import Button from "@mui/material/Button";
import {
  Avatar,
  Box,
  Container,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import ViewListIcon from "@mui/icons-material/ViewList";
import dayjs from "dayjs";
import Link from "next/link";
import HouseholdMemberAddOrEdit from "./HouseholdMemberAddOrEdit";
import AddressAddOrEdit from "./AddrerssAddOrEdit";
import SignaturePadPopup from "./SignaturePadPopup";
import StarLogo from "../../../../public/StarLogo.png";
import InfoCard from "./InfoCard";
import HouseholdMemberForm from "./HouseholdMemberForm";

export default function Home() {
  const [open, setOpen] = useState(false);
  const [signaturePadOpen, setSignaturePadOpen] = useState(false);
  const [addressOpen, setAddressOpen] = useState(false);
  const [houseHolds, setHouseholds] = useState([]);
  const [editHouseHold, setEditHousehold] = useState([]);
  const [signatures, setSignatures] = useState([]);
  const [addresses, setAddresses] = useState([]); // Initialize addresses state
  const [editingAddress, setEditingAddress] = useState(null);

  const handleSaveSignature = (signatureData) => {
    setSignatures([...signatures, signatureData]);
    setSignaturePadOpen(false);
  };

  const handleRemoveSignature = (indexToRemove) => {
    setSignatures(signatures.filter((_, index) => index !== indexToRemove));
  };

  const handleClickOpen = () => setOpen(true);
  const handleClose = () => {
    setEditHousehold(null);
    setOpen(false);
  };

  const handleEditHousehold = (household) => {
    setEditHousehold(household);
    setOpen(true);
  };

  const handleAddNewItem = (houseHold, remove = false) => {
    if (remove) {
      let updatedHouseholds;
      if (houseHold.id) {
        updatedHouseholds = houseHolds.filter((h) => h.id !== houseHold.id);
      } else {
        updatedHouseholds = houseHolds.filter((h) => h.key !== houseHold.key);
      }
      setHouseholds(updatedHouseholds);
      return;
    }
    let householdIndex;
    if (houseHold.id) {
      householdIndex = houseHolds.findIndex((h) => h.id === houseHold.id);
    } else {
      householdIndex = houseHolds.findIndex((h) => h.key === houseHold.key);
    }

    if (householdIndex !== -1) {
      const updatedHouseholds = [...houseHolds];
      updatedHouseholds[householdIndex] = houseHold;
      setHouseholds(updatedHouseholds);
    } else {
      setHouseholds([...houseHolds, houseHold]);
    }
  };

  // Handler to add a new address
  const handleAddAddress = (newAddress) => {
    setAddresses([...addresses, newAddress]);
  };

  // Handler to remove an address
  const handleRemoveAddress = (addressIndex) => {
    setAddresses(addresses.filter((_, index) => index !== addressIndex));
  };

  const handleEditAddressClick = (address, index) => {
    setEditingAddress({ ...address, index }); // Include the index to identify the address being edited
    setAddressOpen(true); // Open the address edit form
  };

  return (
    <>
      <Container maxWidth="md">
        <Image
          src={StarLogo}
          alt="Star Logo"
          style={{ alignSelf: "flex-start", width: "100px", height: "100px" }}
        />

        <Box display="flex" justifyContent="flex-end" mb={2}>
          <Link href="/staff/my-cases" passHref>
            <Button
              variant="contained"
              color="primary"
              startIcon={<ViewListIcon />}
              style={{ textDecoration: "none" }}
            >
              My Cases
            </Button>
          </Link>
        </Box>

        <InfoCard
          title="Your Information"
          subtitle="Applicant personal information"
        >
          <HouseholdMemberForm />
        </InfoCard>

        <Box display="flex" flexDirection="column" gap={2}>
          <Paper>
            <Stack spacing={1} m={2}>
              <Typography variant="h6" color="blue">
                Applicant/Household Member
              </Typography>
              <List sx={{ width: "100%" }}>
                {houseHolds.map((h) => {
                  return (
                    <React.Fragment key={h.key}>
                      <ListItem
                        secondaryAction={
                          <IconButton
                            edge="end"
                            onClick={() => handleEditHousehold(h)}
                          >
                            <EditIcon />
                          </IconButton>
                        }
                        disablePadding
                      >
                        <ListItemAvatar>
                          <Avatar>{`${h.firstName?.charAt(0)}${h.lastName?.charAt(0)}`}</Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            `${h.lastName} ${h.firstName}` +
                            (h.maidenName ? ` (${h.maidenName})` : "")
                          }
                          secondary={`Gender: ${h.gender} DOB: ${dayjs(h.dateOfBirth).format("MM/DD/YYYY")}`}
                        />
                      </ListItem>
                      <Divider variant="inset" component="li" />
                    </React.Fragment>
                  );
                })}
              </List>
              <Button
                mt={2}
                color="primary"
                variant="contained"
                endIcon={<AddIcon />}
                sx={{ alignSelf: "start" }}
                onClick={handleClickOpen}
              >
                Add Household Member
              </Button>
            </Stack>
          </Paper>

          <Paper>
            <Stack spacing={1} m={2}>
              <Typography variant="h6" color="blue">
                Addresses
              </Typography>

              <List sx={{ width: "100%" }}>
                {addresses.map((address, index) => (
                  <React.Fragment key={index}>
                    <ListItem disablePadding>
                      <ListItemText
                        primary={address.streetAddress}
                        secondary={`${address.city}, ${address.state}, ${address.zip}`}
                      />
                      <IconButton
                        edge="end"
                        onClick={() => handleEditAddressClick(address, index)}
                      >
                        <EditIcon />
                      </IconButton>
                    </ListItem>
                    <Divider variant="inset" component="li" />
                  </React.Fragment>
                ))}
              </List>

              <Button
                mt={2}
                color="primary"
                variant="contained"
                endIcon={<AddIcon />}
                sx={{ alignSelf: "start" }}
                onClick={() => setAddressOpen(true)}
              >
                Add Address
              </Button>
            </Stack>
          </Paper>

          <Paper>
            <Stack spacing={1} m={2}>
              <Typography variant="h6" color="blue">
                Signatures
              </Typography>
              <Box sx={{ mt: 2 }}>
                {signatures.map((signature, index) => (
                  <Box
                    key={index}
                    sx={{
                      mb: 1,
                      border: "1px solid #ddd",
                      padding: 1,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Image
                      src={signature}
                      alt={`Signature ${index + 1}`}
                      height="50"
                      width="100"
                    />
                    <Button
                      variant="contained"
                      color="error"
                      onClick={() => handleRemoveSignature(index)}
                    >
                      Remove
                    </Button>
                  </Box>
                ))}
              </Box>
              <Button
                mt={2}
                color="primary"
                variant="contained"
                endIcon={<AddIcon />}
                onClick={() => setSignaturePadOpen(true)}
              >
                Open Signature Pad
              </Button>
            </Stack>
          </Paper>
        </Box>
      </Container>

      <HouseholdMemberAddOrEdit
        isOpen={open}
        initialHousehold={editHouseHold}
        onClose={handleClose}
        onSave={handleAddNewItem}
      />

      <AddressAddOrEdit
        isOpen={addressOpen}
        initialAddress={editingAddress} // Pass the editingAddress state
        onClose={() => {
          setAddressOpen(false);
          setEditingAddress(null); // Reset the editing address state when closing the form
        }}
        onSave={handleAddAddress}
      />

      <SignaturePadPopup
        isOpen={signaturePadOpen}
        onClose={() => setSignaturePadOpen(false)}
        onSave={handleSaveSignature}
      />
    </>
  );
}

import React, { useState, useEffect } from "react";
import { Authorization, ScStatusType } from "../../../../../../types/Authorization";
import { notifications } from "@mantine/notifications";
import axios from "axios";
import { IconCheck } from "@tabler/icons-react";
import {
  Box,
  Card,
  Group,
  Select,
  Stack,
  Text,
  Title,
  Button,
  rem,
  Divider,
  Loader,
} from "@mantine/core";
import { DateInput } from "@mantine/dates";
import urlHelpers from "@/app/urlHelpers";

interface Note {
  id: number;
  note: string;
  createdOn: string;
  createdBy: string;
}

interface ScStatusCardProps {
  authorization: Authorization;
}

const ScStatusCard: React.FC<ScStatusCardProps> = ({ authorization }) => {
  const [scStatus, setScStatus] = useState<number | null>(
    authorization.scStatus || null,
  );
  const [followUpDate, setFollowUpDate] = useState<string | null>(
    authorization.followUpDate
      ? new Date(authorization.followUpDate).toISOString().split("T")[0]
      : null,
  );
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastNote, setLastNote] = useState<Note | null>(null);
  const [isLoadingNotes, setIsLoadingNotes] = useState(false);
  const [statusOptions, setStatusOptions] = useState<{ value: string, label: string }[]>([]);
  const [isLoadingStatusOptions, setIsLoadingStatusOptions] = useState(false);

  useEffect(() => {
    if (authorization?.child?.id) {
      fetchLastNote(authorization.child.id);
    }
    fetchStatusOptions();
  }, [authorization]);

  const fetchStatusOptions = async () => {
    setIsLoadingStatusOptions(true);
    try {
      const response = await axios.get<ScStatusType[]>(
        urlHelpers.getAbsoluteURL("api/my-cases/sc-status-types")
      );
      
      const options = response.data.map(status => ({
        value: status.id.toString(),
        label: status.name
      }));
      
      setStatusOptions(options);
    } catch (error) {
      console.error("Error fetching status options:", error);
      notifications.show({
        title: "Error",
        message: "Failed to load status options",
        color: "red",
        autoClose: 2000,
      });
    } finally {
      setIsLoadingStatusOptions(false);
    }
  };

  const fetchLastNote = async (childId: number) => {
    setIsLoadingNotes(true);
    try {
      const response = await axios.get(
        urlHelpers.getAbsoluteURL(`api/notes/child/${childId}`),
      );

      if (response.data && response.data.length > 0) {
        setLastNote(response.data[response.data.length - 1]);
      }
    } catch (error) {
      console.error("Error fetching notes:", error);
    } finally {
      setIsLoadingNotes(false);
    }
  };

  const handleUpdateStatus = async () => {
    if (!scStatus) return;

    setIsUpdating(true);
    try {
      const response = await axios.post(
        urlHelpers.getAbsoluteURL(`api/my-cases/${authorization.id}/sc-status`),
        {
          scStatus,
          followUpDate,
        },
      );

      const statusName = statusOptions.find(option => option.value === scStatus.toString())?.label || "";
      
      notifications.show({
        title: "Status updated",
        message: `SC Status updated to ${statusName}`,
        icon: <IconCheck style={{ width: rem(18), height: rem(18) }} />,
        loading: false,
        autoClose: 2000,
      });
    } catch (error) {
      console.error("Error updating SC status:", error);
      notifications.show({
        title: "Error",
        message: "Failed to update SC status",
        color: "red",
        autoClose: 2000,
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Stack gap="md">
        <Title order={4}>Authorization Details</Title>

        <Group grow>
          <Box>
            <Text size="sm" c="dimmed">
              Authorization #
            </Text>
            <Text>{authorization.authNumber || "N/A"}</Text>
          </Box>
          <Box>
            <Text size="sm" c="dimmed">
              Status
            </Text>
            <Text>{authorization.statusId}</Text>
          </Box>
        </Group>

        <Group grow>
          <Box>
            <Text size="sm" c="dimmed">
              Start Date
            </Text>
            <Text>
              {authorization.startDate
                ? new Date(authorization.startDate).toLocaleDateString()
                : "N/A"}
            </Text>
          </Box>
          <Box>
            <Text size="sm" c="dimmed">
              End Date
            </Text>
            <Text>
              {authorization.endDate
                ? new Date(authorization.endDate).toLocaleDateString()
                : "N/A"}
            </Text>
          </Box>
        </Group>

        {authorization.child && (
          <>
            <Divider my="sm" />
            <Title order={5}>Referring Physician</Title>
            <Text>{authorization.child.referringPhysicianName || "N/A"}</Text>
            <Text size="sm">
              {authorization.child.physicianPhoneNumber || ""}
            </Text>
            <Text size="sm">
              {authorization.child.physicianEmailAddress || ""}
            </Text>
          </>
        )}

        <Divider my="sm" />
        <Title order={5}>Last Note</Title>
        {isLoadingNotes ? (
          <Box py="md" style={{ display: "flex", justifyContent: "center" }}>
            <Loader size="sm" />
          </Box>
        ) : lastNote ? (
          <Box>
            <Text size="sm" c="dimmed">
              Created on {formatDate(lastNote.createdOn)} by{" "}
              {lastNote.createdBy}
            </Text>
            <Text mt="xs">{lastNote.note}</Text>
          </Box>
        ) : (
          <Text c="dimmed" size="sm">
            No notes available
          </Text>
        )}

        <Divider my="sm" />
        <Title order={5}>Service Coordinator Status</Title>

        {isLoadingStatusOptions ? (
          <Loader size="sm" />
        ) : (
          <Select
            label="SC Status"
            placeholder="Select status"
            value={scStatus?.toString()}
            onChange={(value) => setScStatus(value ? parseInt(value) : null)}
            data={statusOptions}
            clearable
          />
        )}

        <DateInput
          label="Follow-up Date"
          placeholder="Select date"
          value={followUpDate}
          onChange={setFollowUpDate}
          clearable
        />

        <Button
          onClick={handleUpdateStatus}
          loading={isUpdating}
          disabled={!scStatus}
        >
          Update Status
        </Button>
      </Stack>
    </Card>
  );
};

export default ScStatusCard;

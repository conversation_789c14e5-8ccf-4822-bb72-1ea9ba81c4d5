"use client";
import React, { useState } from "react";
import { useMediaQuery } from "@mantine/hooks";
import { Authorization } from "../../../../../types/Authorization";
import { Stack } from "@mantine/core";
import ChildInfo from "@/app/(admin)/admin/patients/ChildInfo";
import Splitter, { Item } from "devextreme-react/splitter";
import MyCasesTable from "./MyCasesTable";
import ScStatusCard from "./components/ScStatusCard";

const MyCasesPage: React.FC = () => {
  const [selectedAuthorization, setSelectedAuthorization] =
    useState<Authorization | null>(null);
  const matches = useMediaQuery("(min-width: 56.25em)");

  const renderMyCasesTable = () => (
    <MyCasesTable
      onSelectAuthorizationChange={(authorization) =>
        setSelectedAuthorization(authorization)
      }
    />
  );

  const renderDetails = () => (
    <Stack gap="md" style={{ height: "100%", overflow: "hidden" }}>
      {selectedAuthorization && (
        <ScStatusCard authorization={selectedAuthorization} />
      )}
      <ChildInfo
        child={selectedAuthorization?.child ?? null}
        showAuthorizationsTab={false}
      />
    </Stack>
  );

  return (
    <Splitter id="splitter">
      <Item
        resizable={true}
        size="75%"
        minSize="70px"
        render={renderMyCasesTable}
      />
      {matches && (
        <Item
          resizable={true}
          minSize="250px"
          render={renderDetails}
          collapsible
        />
      )}
    </Splitter>
  );
};

export default MyCasesPage;

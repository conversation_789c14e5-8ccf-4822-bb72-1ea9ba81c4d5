"use client";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { createStore } from "devextreme-aspnet-data-nojquery";
import DataGrid, {
  Column,
  Pager,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Toolbar,
  Item,
  DataGridRef,
} from "devextreme-react/data-grid";
import Button from "devextreme-react/button";
import urlHelpers from "@/app/urlHelpers";
import dataGridExport from "@/app/Utils/dataGridExport";
import { Authorization } from "../../../../../types/Authorization";
import calculateAge from "@/app/Utils/dateUtils";

const serviceUrl = urlHelpers.getAbsoluteURL("api/my-cases");

interface MyCasesTableProps {
  onSelectAuthorizationChange?: (authorization: Authorization | null) => void;
}

const MyCasesTable = ({ onSelectAuthorizationChange }: MyCasesTableProps) => {
  const dataGridRef = useRef<DataGridRef>(null);
  const [remoteDataSource, setRemoteDataSource] = useState<any>(null);
  const [selectedAuthorization, setSelectedAuthorization] =
    useState<Authorization | null>(null);

  const loadAuthorizationDataSource = useCallback(async () => {
    setRemoteDataSource(
      createStore({
        key: "id",
        loadUrl: serviceUrl,
        onBeforeSend: (e, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
        errorHandler(e) {
          console.error("Error loading data:", e);
        },
      }),
    );
  }, []);

  useEffect(() => {
    loadAuthorizationDataSource();
  }, [loadAuthorizationDataSource]);

  const handleFocusedRowChanging = (e: any) => {
    setSelectedAuthorization(e.row && e.row.data);
    onSelectAuthorizationChange?.(e.row?.data ?? null);
  };

  const formatChildName = (rowData: any) => {
    if (rowData.child) {
      return `${rowData.child.firstName} ${rowData.child.lastName}`;
    }
    return "";
  };

  const formatChildDob = (rowData: any) => {
    if (rowData.child && rowData.child.dateOfBirth) {
      const dob = new Date(rowData.child.dateOfBirth);
      const age = calculateAge(dob);
      return `${age} (${dob.toLocaleDateString()})`;
    }
    return "";
  };

  return (
    <>
      <DataGrid
        remoteOperations
        ref={dataGridRef}
        dataSource={remoteDataSource}
        onExporting={dataGridExport}
        height="100%"
        width="100%"
        allowColumnReordering={true}
        allowColumnResizing={true}
        columnAutoWidth={true}
        showBorders={true}
        columnResizingMode={"widget"}
        showColumnLines={false}
        twoWayBindingEnabled
        rowAlternationEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        onFocusedRowChanged={handleFocusedRowChanging}
      >
        <Toolbar>
          <Item location="before">
            <Button icon="refresh" onClick={loadAuthorizationDataSource} />
          </Item>
          <Item name="searchPanel" locateInMenu="auto" location="before" />
          <Item name="groupPanel" locateInMenu="auto" location="before" />
          <Item name="exportButton" locateInMenu="auto" location="after" />
          <Item
            name="columnChooserButton"
            locateInMenu="auto"
            location="after"
          />
        </Toolbar>
        <RemoteOperations groupPaging={true} />
        <ColumnFixing enabled={true} />
        <SearchPanel visible width={250} />
        <HeaderFilter visible />
        <ColumnChooser enabled />
        <Sorting mode="multiple" />
        <ColumnFixing />
        <Paging defaultPageSize={40} />
        <Pager showPageSizeSelector />
        <FilterPanel visible />
        <FilterBuilderPopup />
        <Export enabled={true} />

        <Column
          dataField="id"
          caption="ID"
          allowEditing={false}
          visible={false}
        />
        <Column caption="Child Name" calculateCellValue={formatChildName} />
        <Column caption="DOB (Age)" calculateCellValue={formatChildDob} />
        <Column dataField="statusId" caption="Status" dataType="string" />
        <Column dataField="scStatusName" caption="SC Status" dataType="string" />
        <Column
          dataField="statusLastUpdated"
          caption="Status Last Updated"
          dataType="date"
        />
        <Column
          dataField="followUpDate"
          caption="Follow Up Date"
          dataType="date"
        />
        <Column dataField="startDate" caption="Start Date" dataType="date" />
        <Column dataField="endDate" caption="End Date" dataType="date" />
        <Column dataField="authNumber" caption="Auth #" dataType="string" />
        <Column dataField="units" caption="Units" dataType="number" />
      </DataGrid>
    </>
  );
};

export default MyCasesTable;

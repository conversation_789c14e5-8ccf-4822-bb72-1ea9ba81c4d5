import { useState, useRef, useCallback, useEffect } from "react";
import Form, {
  EmptyItem,
  GroupItem,
  Item,
  SimpleItem,
  ButtonItem,
  FormTypes,
  FormRef,
} from "devextreme-react/form";
import { RequiredRule } from "devextreme-react/validator";
import Image from "next/image";
import <PERSON><PERSON>ogo from "../../../../../public/StarLogo.png";
import ChildCareIcon from "@mui/icons-material/ChildCare";
import "devextreme-react/text-area";
import "devextreme-react/tag-box";
import "devextreme/dist/css/dx.light.css";
import PersonIcon from "@mui/icons-material/Person";
import LocalHospitalIcon from "@mui/icons-material/LocalHospital";
import AddressInput, {
  SelectedAddress,
} from "@/app/(staff)/staff/AddressInput";
import { ButtonType } from "devextreme-react/common";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import {
  <PERSON>ert,
  Box,
  Button,
  Group,
  LoadingOverlay,
  Paper,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import { IconCircleDashedCheck } from "@tabler/icons-react";
import classes from "./ChildReferralForm.module.css";

const serviceUrl = urlHelpers.getAbsoluteURL("api/public/children");

interface Child {
  status: string | null | undefined;
  firstName: string | null;
  lastName: string | null;
  gender: "M" | "F" | null;
  primaryLanguage?: string | null;
  dateOfBirth: Date | null;
  reasonForReferral: string | null;
  parentName: string | null;
  parentPhoneNumber: string | null;
  parentEmail?: string | null;
  fullAddress?: string | null;
  referringPhysicianName?: string | null;
  physicianPhoneNumber?: string | null;
  physicianEmailAddress?: string | null;
  remeberPhysician?: boolean;
  referralMethod: string;
}

// Define a type that includes the validate method
type FormInstance = {
  validate: () => { isValid: boolean };
  reset: () => void; // Add the reset method
};

const GroupCaption = ({
  iconName,
  caption,
}: {
  iconName: string;
  caption: string;
}) => (
  <Group m={1} gap={10}>
    {iconName === "child" && <ChildCareIcon />}
    {iconName === "parent" && <PersonIcon />}
    {iconName === "physician" && <LocalHospitalIcon />}
    <Title order={3}>{caption}</Title>
  </Group>
);

const groupCaptionNamedRender = (iconName: string) => {
  const groupCaptionRender = (data: any) => (
    <GroupCaption iconName={iconName} {...data} />
  );
  return groupCaptionRender;
};

const ChildReferralForm = () => {
  console.log("ChildReferralForm");
  const formRef = useRef<FormRef & { instance: FormInstance }>(null);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isSuccessfullySubmitted, setIsSuccessfullySubmitted] = useState(false);
  const [child, setChild] = useState<Child | null>({
    status: null,
    firstName: null,
    lastName: null,
    gender: null,
    primaryLanguage: null,
    dateOfBirth: null,
    reasonForReferral: null,
    parentName: null,
    parentPhoneNumber: null,
    parentEmail: null,
    fullAddress: null,
    referringPhysicianName: null,
    physicianPhoneNumber: null,
    physicianEmailAddress: null,
    remeberPhysician: false,
    referralMethod: "Web",
  });

  function resetForm() {
    formRef.current?.instance()?.reset();
    const physicianInfo = localStorage.getItem("physicianInfo");
    if (physicianInfo !== null) {
      const parsedPhysicianInfo = JSON.parse(physicianInfo);
      setChild({
        status: "New",
        firstName: null,
        lastName: null,
        gender: null,
        dateOfBirth: null,
        reasonForReferral: null,
        parentName: null,
        parentPhoneNumber: null,
        parentEmail: null,
        fullAddress: null,
        referringPhysicianName: parsedPhysicianInfo.name,
        physicianPhoneNumber: parsedPhysicianInfo.phoneNumber,
        physicianEmailAddress: parsedPhysicianInfo.email,
        remeberPhysician: true,
        referralMethod: "Web",
      });
    }
  }

  useEffect(() => {
    resetForm();
    setLoading(false);
  }, []);

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setLoading(true);
    setErrorMessage(null);
    try {
      const { isValid } = (formRef.current as any)!.instance().validate();
      if (!isValid) {
        return;
      }

      if (child !== null && child.remeberPhysician) {
        localStorage.setItem(
          "physicianInfo",
          JSON.stringify({
            name: child.referringPhysicianName,
            phoneNumber: child.physicianPhoneNumber,
            email: child.physicianEmailAddress,
          }),
        );
      } else if (child !== null && !child.remeberPhysician) {
        localStorage.removeItem("physicianInfo");
      }

      const childToSubmit = {
        ...child,
        referralMethod: "Web",
      };

      const response = await axios.post(`${serviceUrl}/create`, childToSubmit);
      if (response.status == 200) {
        setIsSuccessfullySubmitted(true);
      } else {
        setErrorMessage("The code is invalid");
      }
    } catch (error) {
      setErrorMessage("An error occurred while validating the code");
    } finally {
      setLoading(false);
    }
  };

  const noAutoComplete = {
    inputAttr: { autoComplete: "new-password" },
  };

  const autoFocus = {
    inputAttr: { ...noAutoComplete, autoFocus: true },
  };

  const genderOptions = {
    items: [
      { value: "M", text: "Male" },
      { value: "F", text: "Female" },
    ],
    valueExpr: "value",
    displayExpr: "text",
    layout: "horizontal",
  };

  const birthDateOptions = {
    openOnFieldClick: false,
    max: new Date(),
    calendarOptions: {
      zoomLevel: "century",
    },
  };

  const notesEditorOptions = { height: 90, maxLength: 200 };

  const primaryLanguageOptions = {
    minSearchLength: 0,
    showDataBeforeSearch: true,
    searchMode: "contains",
    acceptCustomValue: false,
    searchEnabled: true,
    items: [
      "English",
      "Spanish",
      "Chinese",
      "French Creole",
      "Yiddish",
      "Russian",
      "Tagalog (Filipino)",
      "Vietnamese",
      "Arabic",
      "Korean",
      "German",
      "Hindi",
      "Portuguese",
      "Bengali",
      "Japanese",
      "Punjabi",
      "Javanese",
      "Turkish",
      "Italian",
      "Persian (Farsi)",
      "Polish",
      "Other",
    ],
  };

  const phoneEditorOptions = {
    mask: "+1 (X00) 000-0000",
    maskRules: { X: /[02-9]/ },
    inputAttr: { autoComplete: "star_eip_randome" },
  };

  const handleAddressSelected = (address: SelectedAddress) => {
    setChild((prevChild: Child | null) => ({
      ...(prevChild as Child),
      fullAddress: address.formattedAddress,
    }));
  };

  const renderAddressInput = () => {
    return (
      <AddressInput
        value={child?.fullAddress || ""}
        onValueChanged={(e) =>
          setChild((prevChild: Child | null) => ({
            ...(prevChild as Child),
            fullAddress: e,
          }))
        }
        onAddressSelected={handleAddressSelected}
      />
    );
  };

  const [resetButtonOptions, setResetButtonOptions] = useState({
    disabled: true,
    icon: "refresh",
    text: "Reset",
    width: "120px",
    onClick: resetForm,
  });

  const registerButtonOptions = {
    text: "Submit",
    icon: "send",
    type: "default" as ButtonType,
    useSubmitBehavior: true,
    width: "120px",
  };

  const onOptionChanged = useCallback(
    (e: FormTypes.OptionChangedEvent) => {
      if (e.name === "isDirty") {
        setResetButtonOptions({ ...resetButtonOptions, disabled: !e.value });
      }
    },
    [resetButtonOptions, setResetButtonOptions],
  );

  function SuccessfullySubmitted() {
    const icon = <IconCircleDashedCheck />;
    return (
      <Alert variant="light">
        <Stack py={50} align="center" gap={30}>
          <Title textWrap="wrap">Child referral successfully submitted!</Title>
          <IconCircleDashedCheck size="150px" />
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              resetForm();
              setIsSuccessfullySubmitted(false);
            }}
          >
            Submit Another Child
          </Button>
        </Stack>
      </Alert>
    );
  }

  function PageHeader() {
    return (
      <Box m={0} p={24} className="headerBox">
        <Group justify="center" align="center">
          <Image
            src={StarLogo}
            alt="Star Logo"
            style={{
              maxWidth: "100px",
              maxHeight: "100px",
              alignSelf: "flex-start",
            }}
          />
          <Title order={1} fw={600} className={classes.header}>
            Child Referral Form
          </Title>
        </Group>
        <Text className={classes.subheader}>
          If a child can&apos;t learn the way we teach, Star EIP will teach them
          the way they learn best.
        </Text>
      </Box>
    );
  }

  return (
    <Paper className="main-container ">
      <title>Star EIP Referral</title>
      <PageHeader />

      <Box pos="relative">
        <LoadingOverlay
          visible={loading}
          zIndex={1000}
          overlayProps={{ radius: "sm", blur: 2 }}
        />
        <form
          onSubmit={handleSubmit}
          style={{
            display: isSuccessfullySubmitted ? "none" : "block",
            height: "100%",
            margin: "25px",
            paddingBottom: "25px",
          }}
        >
          <Form
            labelMode="floating"
            labelLocation="left"
            ref={formRef}
            formData={child}
            onOptionChanged={onOptionChanged}
            showValidationSummary={true}
          >
            <GroupItem
              colCount={2}
              captionRender={groupCaptionNamedRender("child")}
              caption="Child Information"
            >
              <Item dataField="firstName" editorOptions={autoFocus}>
                <RequiredRule message="First Name is required" />
              </Item>
              <Item dataField="lastName" editorOptions={noAutoComplete}>
                <RequiredRule message="Last Name is required" />
              </Item>
              <Item
                dataField="gender"
                editorType="dxSelectBox"
                editorOptions={genderOptions}
              >
                <RequiredRule message="Gender is required" />
              </Item>
              <SimpleItem
                dataField="primaryLanguage"
                editorType="dxSelectBox"
                editorOptions={primaryLanguageOptions}
              />
              <Item
                dataField="dateOfBirth"
                editorType="dxDateBox"
                editorOptions={birthDateOptions}
              >
                <RequiredRule message="Date Of Birth is required" />
              </Item>
              <EmptyItem />
              <Item
                dataField="reasonForReferral"
                colSpan={2}
                editorType="dxTextArea"
                editorOptions={notesEditorOptions}
              >
                <RequiredRule message="Reason For Referral is required" />
              </Item>
            </GroupItem>

            <GroupItem
              colCount={2}
              caption="Parent Information"
              captionRender={groupCaptionNamedRender("parent")}
            >
              <Item dataField="parentName">
                <RequiredRule message="Parent Name is required" />
              </Item>
              <Item
                dataField="parentPhoneNumber"
                editorOptions={phoneEditorOptions}
              >
                <RequiredRule message="Parent Phone Number is required" />
              </Item>
              <Item dataField="parentEmail" editorOptions={noAutoComplete} />
              <Item dataField="fullAddress" render={renderAddressInput}>
                <RequiredRule message="Address is required" />
              </Item>
            </GroupItem>

            <GroupItem
              caption="Referring Physician Information"
              captionRender={groupCaptionNamedRender("physician")}
            >
              <Item dataField="referringPhysicianName" caption="Name" />
              <Item
                dataField="physicianPhoneNumber"
                caption="Number"
                editorOptions={phoneEditorOptions}
              />
              <Item
                dataField="physicianEmailAddress"
                caption="Email Address"
                editorOptions={noAutoComplete}
              />
              <Item
                dataField="remeberPhysician"
                caption="Remember Physician"
                editorType="dxCheckBox"
              />
            </GroupItem>

            <GroupItem cssClass="buttons-group" colCount={2} colSpan={2}>
              <ButtonItem buttonOptions={resetButtonOptions} name="Reset" />
              <ButtonItem buttonOptions={registerButtonOptions} />
            </GroupItem>
          </Form>
        </form>

        {errorMessage && (
          <Box mt={3}>
            <Alert variant="filled" color="rgba(255, 0, 8, 1)">
              <Stack align="center" justify="space-between">
                {errorMessage}
                <Button variant="contained" onClick={handleSubmit}>
                  Try Again
                </Button>
              </Stack>
            </Alert>
          </Box>
        )}
      </Box>

      {isSuccessfullySubmitted && <SuccessfullySubmitted />}
    </Paper>
  );
};

export default ChildReferralForm;

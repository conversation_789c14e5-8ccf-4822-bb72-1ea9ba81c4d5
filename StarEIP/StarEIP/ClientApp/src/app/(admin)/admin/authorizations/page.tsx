"use client";
import React, { useEffect, useState } from "react";
import AuthorizationsTable from "./AuthorizationsTable";
import { useMediaQuery } from "@mantine/hooks";
import { Authorization, ScStatusType } from "../../../../../types/Authorization";
import { Stack, Select, Loader, Text, Drawer } from "@mantine/core";
import ChildInfo from "@/app/(admin)/admin/patients/ChildInfo";
import Splitter, { Item } from "devextreme-react/splitter";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import { showNotification } from "@/app/Utils/notificationUtils";
import { ChildDetailsDtoSchema } from "@/api/types";
import { useAuthorizationStore } from "@/app/(admin)/admin/authorizations/AuthorizationStore";

const AuthorizationsPage: React.FC = () => {
  const { dataGridRef } = useAuthorizationStore();
  const [selectedAuthorization, setSelectedAuthorization] =
    useState<Authorization | null>(null);
  const matches = useMediaQuery("(min-width: 56.25em)");
  const [scStatus, setScStatus] = useState<number | null>(null);
  const [followUpDate, setFollowUpDate] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [statusOptions, setStatusOptions] = useState<{ value: string, label: string }[]>([]);
  const [isLoadingStatusOptions, setIsLoadingStatusOptions] = useState(false);
  const [selectedChild, setSelectedChild] = useState<ChildDetailsDtoSchema | null>(null);
  const [showChildPanel, setShowChildPanel] = useState(false);

  useEffect(() => {
    fetchStatusOptions();
  }, []);

  useEffect(() => {
    if (selectedAuthorization) {
      setScStatus(selectedAuthorization.scStatus || null);
      setFollowUpDate(
        selectedAuthorization.followUpDate
          ? new Date(selectedAuthorization.followUpDate).toISOString().split("T")[0]
          : null
      );
    }
  }, [selectedAuthorization]);

  const fetchStatusOptions = async () => {
    setIsLoadingStatusOptions(true);
    try {
      const response = await axios.get<ScStatusType[]>(
        urlHelpers.getAbsoluteURL("api/authorizations/sc-status-types")
      );

      const options = response.data.map(status => ({
        value: status.id.toString(),
        label: status.name
      }));

      setStatusOptions(options);
    } catch (error) {
      console.error("Error fetching status options:", error);
      showNotification("error", "Failed to load status options");
    } finally {
      setIsLoadingStatusOptions(false);
    }
  };

  const handleUpdateStatus = async (statusToUpdate?: number) => {
    const statusValue = statusToUpdate ?? scStatus;
    if (!statusValue || !selectedAuthorization) return;

    setIsUpdating(true);
    try {
      await axios.post(
        urlHelpers.getAbsoluteURL(`api/authorizations/${selectedAuthorization.id}/sc-status`),
        {
          scStatus: statusValue,
          followUpDate,
        },
      );

      const statusName = statusOptions.find(option => option.value === statusValue.toString())?.label || "";

      showNotification("success", `SC Status updated to ${statusName}`);

      // Update the selected authorization with new data
      const updatedAuthorization = {
        ...selectedAuthorization,
        scStatus: statusValue,
        scStatusName: statusName,
        scStatusLastUpdated: new Date(),
        followUpDate: followUpDate ? new Date(followUpDate) : undefined
      };

      setSelectedAuthorization(updatedAuthorization);

      // Refresh the specific row in the grid
      if (dataGridRef && dataGridRef.current) {
        const grid = dataGridRef.current.instance();
        const rowIndex = grid.getRowIndexByKey(selectedAuthorization.id);
        if (rowIndex >= 0) {
          grid.refresh();
        }
      }
    } catch (error) {
      console.error("Error updating SC status:", error);
      showNotification("error", "Failed to update SC status");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleChildNameClick = async (childId: number) => {
    try {
      const response = await axios.get(
        urlHelpers.getAbsoluteURL(`api/children/${childId}`)
      );
      setSelectedChild(response.data);
      setShowChildPanel(true);
    } catch (error) {
      console.error("Error fetching child details:", error);
      showNotification("error", "Failed to load child details");
    }
  };

  const handleScStatusChange = async (value: string | null) => {
    if (!selectedAuthorization) return;

    const newScStatus = value ? parseInt(value) : null;
    setScStatus(newScStatus);

    if (newScStatus) {
      await handleUpdateStatus(newScStatus);
    }
  };

  const renderAuthorizationsTable = () => (
    <AuthorizationsTable
      onSelectAuthorizationChange={(authorization) =>
        setSelectedAuthorization(authorization)
      }
      onChildNameClick={handleChildNameClick}
    />
  );

  const renderDetails = () => (
    <Stack gap="md" style={{ height: "100%", overflow: "hidden" }} p="md">
      {selectedAuthorization && (
        <>


          {isLoadingStatusOptions ? (
            <Loader size="md" />
          ) : (
            <Select
              size="xs"
              label="SC Status"
              placeholder="Select status"
              value={scStatus?.toString()}
              onChange={handleScStatusChange}
              data={statusOptions}
              clearable
              disabled={isUpdating}
            />
          )}
          {selectedAuthorization.scStatusLastUpdated && (
            <Text size="xs" c="dimmed">
              Last updated: {new Date(selectedAuthorization.scStatusLastUpdated).toLocaleString()}
            </Text>
          )}

          {isUpdating && <Text size="sm" c="dimmed">Updating...</Text>}
        </>
      )}
    </Stack>
  );

  return (
    <>
      <Splitter id="splitter">
        <Item
          resizable={true}
          size="75%"
          minSize="70px"
          render={renderAuthorizationsTable}
        />
        {matches && (
          <Item
            resizable={true}
            minSize="250px"
            render={renderDetails}
            collapsible
          />
        )}
      </Splitter>

      {/* Child Info Side Panel */}
      <Drawer
        opened={showChildPanel}
        onClose={() => setShowChildPanel(false)}
        position="right"
        withCloseButton={false}
        size="lg"
        overlayProps={{ backgroundOpacity: 0.5, blur: 4 }}
      >
        {selectedChild && (
          <ChildInfo
            child={selectedChild}
            showAuthorizationsTab={false}
          />
        )}
      </Drawer>
    </>
  );
};

export default AuthorizationsPage;

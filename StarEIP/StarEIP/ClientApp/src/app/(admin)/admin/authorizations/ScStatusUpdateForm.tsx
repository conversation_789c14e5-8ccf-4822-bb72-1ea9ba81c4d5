import React, { useState, useEffect } from "react";
import { Authorization, ScStatusType } from "../../../../../types/Authorization";
import { notifications } from "@mantine/notifications";
import axios from "axios";
import { IconCheck } from "@tabler/icons-react";
import {
  Box,
  Card,
  Select,
  Stack,
  Text,
  Title,
  Button,
  rem,
  Divider,
  Loader,
} from "@mantine/core";
import { DateInput } from "@mantine/dates";
import urlHelpers from "@/app/urlHelpers";

interface ScStatusUpdateFormProps {
  authorization: Authorization;
  onStatusUpdated?: () => void;
}

const ScStatusUpdateForm: React.FC<ScStatusUpdateFormProps> = ({ 
  authorization,
  onStatusUpdated
}) => {
  const [scStatus, setScStatus] = useState<number | null>(
    authorization.scStatus || null,
  );
  const [followUpDate, setFollowUpDate] = useState<string | null>(
    authorization.followUpDate
      ? new Date(authorization.followUpDate).toISOString().split("T")[0]
      : null,
  );
  const [isUpdating, setIsUpdating] = useState(false);
  const [statusOptions, setStatusOptions] = useState<{ value: string, label: string }[]>([]);
  const [isLoadingStatusOptions, setIsLoadingStatusOptions] = useState(false);

  useEffect(() => {
    fetchStatusOptions();
  }, []);

  useEffect(() => {
    setScStatus(authorization.scStatus || null);
    setFollowUpDate(
      authorization.followUpDate
        ? new Date(authorization.followUpDate).toISOString().split("T")[0]
        : null
    );
  }, [authorization]);

  const fetchStatusOptions = async () => {
    setIsLoadingStatusOptions(true);
    try {
      const response = await axios.get<ScStatusType[]>(
        urlHelpers.getAbsoluteURL("api/authorizations/sc-status-types")
      );
      
      const options = response.data.map(status => ({
        value: status.id.toString(),
        label: status.name
      }));
      
      setStatusOptions(options);
    } catch (error) {
      console.error("Error fetching status options:", error);
      notifications.show({
        title: "Error",
        message: "Failed to load status options",
        color: "red",
        autoClose: 2000,
      });
    } finally {
      setIsLoadingStatusOptions(false);
    }
  };

  const handleUpdateStatus = async () => {
    if (!scStatus) return;

    setIsUpdating(true);
    try {
      const response = await axios.post(
        urlHelpers.getAbsoluteURL(`api/authorizations/${authorization.id}/sc-status`),
        {
          scStatus,
          followUpDate,
        },
      );

      const statusName = statusOptions.find(option => option.value === scStatus.toString())?.label || "";
      
      notifications.show({
        title: "Status updated",
        message: `SC Status updated to ${statusName}`,
        icon: <IconCheck style={{ width: rem(18), height: rem(18) }} />,
        loading: false,
        autoClose: 2000,
      });
      
      if (onStatusUpdated) {
        onStatusUpdated();
      }
    } catch (error) {
      console.error("Error updating SC status:", error);
      notifications.show({
        title: "Error",
        message: "Failed to update SC status",
        color: "red",
        autoClose: 2000,
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Stack gap="md">
        <Title order={4}>Service Coordinator Status</Title>
        
        <Text size="sm" c="dimmed">
          Current SC Status
        </Text>
        <Text>{authorization.scStatusName || "Not set"}</Text>
        
        <Divider my="sm" />
        
        <Title order={5}>Update SC Status</Title>

        {isLoadingStatusOptions ? (
          <Loader size="sm" />
        ) : (
          <Select
            label="SC Status"
            placeholder="Select status"
            value={scStatus?.toString()}
            onChange={(value) => setScStatus(value ? parseInt(value) : null)}
            data={statusOptions}
            clearable
          />
        )}

        <DateInput
          label="Follow-up Date"
          placeholder="Select date"
          value={followUpDate}
          onChange={setFollowUpDate}
          clearable
        />

        <Button
          onClick={handleUpdateStatus}
          loading={isUpdating}
          disabled={!scStatus}
        >
          Update Status
        </Button>
      </Stack>
    </Card>
  );
};

export default ScStatusUpdateForm;


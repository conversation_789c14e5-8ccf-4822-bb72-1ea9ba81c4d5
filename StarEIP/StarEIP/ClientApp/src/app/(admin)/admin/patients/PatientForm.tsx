"use client";

import React, { useCallback, useRef, useState } from "react";
import { Group, Title } from "@mantine/core";
import ChildCareIcon from "@mui/icons-material/ChildCare";
import PersonIcon from "@mui/icons-material/Person";
import LocalHospitalIcon from "@mui/icons-material/LocalHospital";
import Form, {
  ButtonItem,
  EmptyItem,
  FormRef,
  FormTypes,
  GroupItem,
  Item,
  SimpleItem,
} from "devextreme-react/form";
import AddressInput, {
  SelectedAddress,
} from "@/app/(staff)/staff/AddressInput";
import { ButtonType } from "devextreme-react/common";
import { RequiredRule } from "devextreme-react/validator";
import "devextreme-react/text-area";
import axios from "axios";
import { ChildDetailsDtoSchema } from "@/api/types";

interface ChildDetailsFormProps {
  onSubmit: (child: ChildDetailsDtoSchema) => void;
  onCancel: () => void;
  initialData?: ChildDetailsDtoSchema;
  isEditMode?: boolean;
  physicians: Array<{ id: number; name: string }>;
}

type FormInstance = {
  validate: () => { isValid: boolean };
  reset: () => void; // Add the reset method
};

const GroupCaption = ({
  iconName,
  caption,
}: {
  iconName: string;
  caption: string;
}) => (
  <Group m={1} gap={10}>
    {/* {iconName === 'child' && <ChildCareIcon />}
        {iconName === 'parent' && <PersonIcon />}
        {iconName === 'physician' && <LocalHospitalIcon />} */}
    <Title order={3}>{caption}</Title>
  </Group>
);

const groupCaptionNamedRender = (iconName: string) => {
  const groupCaptionRender = (data: any) => (
    <GroupCaption iconName={iconName} {...data} />
  );
  return groupCaptionRender;
};

const PatientForm: React.FC<ChildDetailsFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  isEditMode = false,
  physicians,
}) => {
  const formRef = useRef<FormRef & { instance: FormInstance }>(null);

  // Map gender to the correct format if needed
  let mappedGender = initialData?.gender;
  if (mappedGender && mappedGender !== "M" && mappedGender !== "F") {
    const genderLower = mappedGender.toLowerCase();
    if (genderLower === "m" || genderLower === "male") {
      mappedGender = "M";
    } else if (genderLower === "f" || genderLower === "female") {
      mappedGender = "F";
    }
  }

  const [child, setChild] = useState<ChildDetailsDtoSchema>({
    ...(initialData || {}),
    id: initialData?.id || 0,
    status: initialData?.status || "New",
    firstName: initialData?.firstName,
    lastName: initialData?.lastName,
    gender: mappedGender, // Use the mapped gender
    primaryLanguage: initialData?.primaryLanguage,
    dateOfBirth: initialData?.dateOfBirth,
    reasonForReferral: initialData?.reasonForReferral,
    parentName: initialData?.parentName,
    parentPhoneNumber: initialData?.parentPhoneNumber,
    parentEmail: initialData?.parentEmail,
    fullAddress: initialData?.fullAddress,
    referringPhysicianName: initialData?.referringPhysicianName,
    physicianPhoneNumber: initialData?.physicianPhoneNumber,
    physicianEmailAddress: initialData?.physicianEmailAddress,
    referralMethod: initialData?.referralMethod || undefined,
    referringPhysicianId: initialData?.referringPhysicianId,
  });

  function resetForm() {
    formRef.current?.instance()?.reset();
    setChild({
      id: child.id,
      status: "New",
      firstName: undefined,
      lastName: undefined,
      primaryLanguage: undefined,
      dateOfBirth: undefined,
      reasonForReferral: undefined,
      parentName: undefined,
      parentPhoneNumber: undefined,
      parentEmail: undefined,
      fullAddress: undefined,
      referringPhysicianName: undefined,
      physicianPhoneNumber: undefined,
      physicianEmailAddress: undefined,
      referralMethod: undefined,
    });
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formRef.current) {
      console.error("formRef.current is not defined");
      return;
    }
    const { isValid } = formRef.current.instance().validate();
    if (!isValid) return;
    console.log("submit data", child);
    try {
      onSubmit(child);
    } catch (error: any) {
      console.error("Error submitting data:", error);

      if (axios.isAxiosError(error) && error.response) {
        // Log the detailed backend error response
        console.error("Backend error response:", error.response.data);
      }
    }
  };

  const noAutoComplete = {
    inputAttr: { autoComplete: "new-password" },
  };

  const autoFocus = {
    inputAttr: { ...noAutoComplete, autoFocus: true },
  };

  const genderOptions = {
    items: [
      { value: "M", text: "Male" },
      { value: "F", text: "Female" },
    ],
    valueExpr: "value",
    displayExpr: "text",
    layout: "horizontal",
  };

  const birthDateOptions = {
    openOnFieldClick: false,
    max: new Date(),
    calendarOptions: {
      zoomLevel: "century",
    },
  };

  const notesEditorOptions = { height: 90, maxLength: 200 };

  const primaryLanguageOptions = {
    minSearchLength: 0,
    showDataBeforeSearch: true,
    searchMode: "contains",
    acceptCustomValue: false,
    searchEnabled: true,
    items: [
      "English",
      "Spanish",
      "Chinese",
      "French Creole",
      "Yiddish",
      "Russian",
      "Tagalog (Filipino)",
      "Vietnamese",
      "Arabic",
      "Korean",
      "German",
      "Hindi",
      "Portuguese",
      "Bengali",
      "Japanese",
      "Punjabi",
      "Javanese",
      "Turkish",
      "Italian",
      "Persian (Farsi)",
      "Polish",
      "Other",
    ],
  };

  const phoneEditorOptions = {
    mask: "+1 (X00) 000-0000",
    maskRules: { X: /[02-9]/ },
    inputAttr: { autoComplete: "star_eip_randome" },
  };

  const referralMethodOptions = {
    items: ["Web", "Fax", "Call", "Email", "Equipo", "HUB"],
    value: child.referralMethod,
  };

  const handleAddressSelected = (address: SelectedAddress) => {
    setChild((prevChild: ChildDetailsDtoSchema | null) => ({
      ...(prevChild as ChildDetailsDtoSchema),
      fullAddress: address.formattedAddress,
    }));
  };

  const renderAddressInput = () => {
    return (
      <AddressInput
        value={child?.fullAddress || ""}
        onValueChanged={(e) => {
          setChild((prevChild: ChildDetailsDtoSchema | null) => ({
            ...(prevChild as ChildDetailsDtoSchema),
            fullAddress: e,
          }));
        }}
        onAddressSelected={handleAddressSelected}
      />
    );
  };

  const physicianOptions = {
    dataSource: physicians,
    displayExpr: "name",
    valueExpr: "id",
    searchEnabled: true,
    onValueChanged: (e: { value: number }) => {
      const selectedPhysician = physicians.find((p) => p.id === e.value);
      if (selectedPhysician) {
        setChild((prevChild) => ({
          ...prevChild,
          referringPhysicianName: selectedPhysician.name,
          referringPhysicianId: selectedPhysician.id,
        }));
      }
    },
  };

  const [resetButtonOptions, setResetButtonOptions] = useState({
    disabled: true,
    icon: "refresh",
    text: "Reset",
    width: "120px",
    onClick: resetForm,
  });

  const registerButtonOptions = {
    text: "Save",
    icon: "send",
    type: "default" as ButtonType,
    useSubmitBehavior: true,
    width: "120px",
  };

  const onOptionChanged = useCallback(
    (e: FormTypes.OptionChangedEvent) => {
      if (e.name === "isDirty") {
        setResetButtonOptions({ ...resetButtonOptions, disabled: !e.value });
      }
    },
    [resetButtonOptions, setResetButtonOptions],
  );

  const cancelButtonOptions = {
    text: "Cancel",
    icon: "close",
    type: "normal" as ButtonType,
    width: "120px",
    onClick: onCancel,
  };

  return (
    <form onSubmit={handleSubmit} id="child-details-form">
      <Form
        ref={formRef}
        formData={child}
        labelMode="floating"
        labelLocation="left"
        onOptionChanged={onOptionChanged}
        showValidationSummary={true}
        //colCountByScreen={{ "sm": 1, "md": 2, "lg": 2, "xl": 2 }}
      >
        <GroupItem
          colCount={2}
          captionRender={groupCaptionNamedRender("child")}
          caption="Child Information"
        >
          <Item dataField="firstName" editorOptions={autoFocus}>
            <RequiredRule message="First Name is required" />
          </Item>
          <Item dataField="lastName" editorOptions={noAutoComplete}>
            <RequiredRule message="Last Name is required" />
          </Item>
          <Item
            dataField="gender"
            editorType="dxSelectBox"
            editorOptions={genderOptions}
          />
          <SimpleItem
            dataField="primaryLanguage"
            editorType="dxSelectBox"
            editorOptions={primaryLanguageOptions}
          />
          <Item
            dataField="dateOfBirth"
            editorType="dxDateBox"
            editorOptions={birthDateOptions}
          />
          <EmptyItem />
          <Item
            dataField="reasonForReferral"
            colSpan={2}
            editorType="dxTextArea"
            editorOptions={notesEditorOptions}
          />
        </GroupItem>

        <GroupItem
          colCount={2}
          caption="Parent Information"
          captionRender={groupCaptionNamedRender("parent")}
        >
          <Item dataField="parentName" />
          <Item
            dataField="parentPhoneNumber"
            editorOptions={phoneEditorOptions}
          />
          <Item dataField="parentEmail" editorOptions={noAutoComplete} />
          <Item dataField="fullAddress" render={renderAddressInput} />
        </GroupItem>

        <GroupItem
          caption="Referring Physician Information"
          captionRender={groupCaptionNamedRender("physician")}
        >
          <Item
            dataField="referringPhysicianId"
            editorType="dxSelectBox"
            editorOptions={physicianOptions}
            caption="Referring Physcician"
          ></Item>
          <Item dataField="referringPhysicianName" caption="Name" />
          <Item
            dataField="physicianPhoneNumber"
            caption="Number"
            editorOptions={phoneEditorOptions}
          />
          <Item
            dataField="physicianEmailAddress"
            caption="Email Address"
            editorOptions={noAutoComplete}
          />
        </GroupItem>

        <GroupItem caption="Additional Information">
          <Item
            dataField="referralMethod"
            editorType="dxSelectBox"
            editorOptions={referralMethodOptions}
          >
            <RequiredRule message="Referral Method is required" />
          </Item>
          <Item dataField="programId" editorType="dxNumberBox" />
          <Item dataField="providerSoftChildId" editorType="dxNumberBox" />
        </GroupItem>

        <GroupItem cssClass="buttons-group" colCount={3} colSpan={2}>
          <ButtonItem buttonOptions={cancelButtonOptions} name="Cancel" />
          <ButtonItem buttonOptions={resetButtonOptions} name="Reset" />
          <ButtonItem buttonOptions={registerButtonOptions} />
        </GroupItem>
      </Form>
    </form>
  );
};

export default PatientForm;

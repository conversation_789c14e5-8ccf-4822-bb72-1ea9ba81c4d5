"use client";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { createStore } from "devextreme-aspnet-data-nojquery";
import DataGrid, {
  DataGridRef,
  Column,
  Pager,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Toolbar,
  Item,
  GroupPanel,
  FilterRow,
} from "devextreme-react/data-grid";
import Button from "devextreme-react/button";
import urlHelpers from "../../../urlHelpers";
import dataGridExport from "../../../Utils/dataGridExport";
import { useRouter } from "next/navigation";
import calculateAge from "../../../Utils/dateUtils";
import Link from "next/link";
import { useMediaQuery } from "@mantine/hooks";
import { Badge, Drawer, em } from "@mantine/core";
import ChildInfo from "./ChildInfo";
import { usePatientStore } from "./PatientStore";
import { ChildDetailsDtoSchema } from "@/api/types";
import PatientHoverCard from "./components/PatientHoverCard";

const serviceUrl = urlHelpers.getAbsoluteURL("api/children");

interface PatientTableOptions {
  showDateOfBirth?: boolean;
  defaultFilter?: any;
  showHeaderFilter?: boolean;
}

interface ChildrenTableProps {
  item?: React.ReactNode;
  onFocusedChildChanging?: (child: ChildDetailsDtoSchema | null) => void;
  showToolbar?: boolean;
  options?: PatientTableOptions;
}

const ChildrenTable = ({
  item,
  onFocusedChildChanging,
  showToolbar,
  options,
}: ChildrenTableProps) => {
  const {
    remoteDataSource,
    setRemoteDataSource,
    setDataGridRef,
    setSelectedChild,
    selectedChild,
  } = usePatientStore();
  const [mobileRowClick, setMobileRowClick] =
    useState<ChildDetailsDtoSchema | null>();
  const router = useRouter();
  const dataGridRef = useRef<DataGridRef>(null);
  const isMobile = useMediaQuery(`(max-width: ${em(750)})`);
  const [isHeaderFilterVisible, setIsHeaderFilterVisible] = useState(
    options?.showHeaderFilter ?? false,
  );

  const loadChildDataSource = useCallback(async () => {
    setRemoteDataSource(
      createStore({
        key: "id",
        loadUrl: serviceUrl,
        deleteUrl: `${serviceUrl}/Delete`,
        onBeforeSend: (_, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
        errorHandler(e) {
          if (e.message === "Unauthorized") {
            router.push("/login");
          }
        },
      }),
    );
  }, [router, setRemoteDataSource]);

  useEffect(() => {
    loadChildDataSource();
  }, [loadChildDataSource]);

  useEffect(() => {
    setDataGridRef(dataGridRef); // Store the ref in Zustand on mount
  }, [setDataGridRef]);

  const handleFocusedRowChanging = (e: any) => {
    setTimeout(async () => {
      setSelectedChild(e.row && e.row.data);
      if (onFocusedChildChanging) {
        onFocusedChildChanging(e.row.data);
      }
    }, 175);
  };

  const handleRowUpdated = (e: any) => {
    onFocusedChildChanging?.(e.data);
  };

  const handleAddClick = useCallback(() => {
    router.push("/admin/patients/new");
  }, [router]);

  const handleDeleteClick = useCallback(async () => {
    const grid = dataGridRef?.current?.instance();
    if (selectedChild && grid) {
      const rowIndex = grid.getRowIndexByKey(selectedChild.id);
      grid.deleteRow(rowIndex);
    }
  }, [selectedChild]);

  const renderIdCell = (data: { data: ChildDetailsDtoSchema }) => {
    return (
      <PatientHoverCard childDetails={data.data}>
        <Link href={`/admin/patients/${data.data.id}`} scroll={false}>
          {data.data.id}
        </Link>
      </PatientHoverCard>
    );
  };

  const referralMethodRender = (data: { value: string }) => {
    return !data.value || data.value === "Unknown" ? "N/A" : data.value;
  };

  const handleRowClick = useCallback(
    (e: any) => {
      if (isMobile) {
        setMobileRowClick(e.data);
      }
    },
    [isMobile],
  );

  const toggleHeaderFilter = () => {
    setIsHeaderFilterVisible((prev) => !prev);
  };

  // Don't render DataGrid until we have a data source
  if (!remoteDataSource) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <DataGrid
        remoteOperations
        ref={dataGridRef}
        dataSource={remoteDataSource}
        onExporting={dataGridExport}
        height="100%"
        width="100%"
        allowColumnReordering={true}
        allowColumnResizing={true}
        columnAutoWidth={true}
        showBorders={true}
        columnResizingMode={"widget"}
        showColumnLines={false}
        twoWayBindingEnabled
        rowAlternationEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        onFocusedRowChanged={handleFocusedRowChanging}
        onRowUpdated={handleRowUpdated}
        onRowClick={handleRowClick}
        defaultFilterValue={options?.defaultFilter}
      >
        <Toolbar>
          <Item name="searchPanel" locateInMenu="auto" location="before" />
          <Item location="before" visible={showToolbar}>
            <Button icon="refresh" onClick={loadChildDataSource} />
          </Item>
          <Item location="before" visible={showToolbar}>
            <Button icon="plus" onClick={handleAddClick} />
          </Item>
          <Item location="before" visible={showToolbar}>
            <Button
              icon="remove"
              disabled={!selectedChild}
              onClick={handleDeleteClick}
            />
          </Item>
          <Item location="before" visible={showToolbar}>
            <Button
              icon="filter"
              type={isHeaderFilterVisible ? "default" : "normal"}
              onClick={toggleHeaderFilter}
            />
          </Item>
          <Item
            name="groupPanel"
            locateInMenu="auto"
            location="before"
            visible={showToolbar}
          />
          <Item
            name="exportButton"
            locateInMenu="auto"
            location="after"
            visible={showToolbar}
          />
          <Item
            name="applyFilterButton"
            locateInMenu="auto"
            location="after"
            visible={showToolbar}
          />
          <Item
            name="revertButton"
            locateInMenu="auto"
            location="after"
            visible={showToolbar}
          />
          <Item
            name="saveButton"
            locateInMenu="auto"
            location="after"
            visible={showToolbar}
          />
          <Item
            name="columnChooserButton"
            locateInMenu="auto"
            location="after"
          />
          {item && <Item location="after">{item}</Item>}
        </Toolbar>
        <GroupPanel visible={true} />
        <RemoteOperations groupPaging={true} />
        <ColumnFixing enabled={true} />
        <SearchPanel visible width={250} />
        <ColumnChooser enabled />
        <Sorting mode="multiple" />
        <ColumnFixing />
        <Paging defaultPageSize={20} />
        <Pager
          showPageSizeSelector
          showInfo
          allowedPageSizes={[10, 20, 50, 100, 500]}
          showNavigationButtons
          infoText="Page {0} of {1} (Total Rows: {2})"
          visible={true}
        />

        <HeaderFilter visible />
        <FilterPanel visible={isHeaderFilterVisible} />
        <FilterRow visible={isHeaderFilterVisible} />

        <FilterBuilderPopup />
        <Export enabled={true} />

        <Column
          dataField="id"
          caption="ID"
          allowEditing={false}
          cellRender={renderIdCell}
          sortOrder="desc"
        />
        <Column
          dataField="status"
          caption="Status"
          dataType="string"
          width={175}
          cellRender={(data) => <Badge color="cyan">{data.value}</Badge>}
          allowSearch={false}
        />
        <Column
          dataField="firstName"
          caption="First Name"
          dataType="string"
          visible={false}
        />
        <Column
          dataField="lastName"
          caption="Last Name"
          dataType="string"
          visible={false}
        />
        <Column dataField="patientName" caption="Patient" dataType="string" />
        <Column
          dataField="dateOfBirth"
          caption="DOB"
          dataType="date"
          width={175}
          visible={options?.showDateOfBirth ?? false}
        />
        <Column
          caption="Age"
          dataType="number"
          allowSorting={true}
          calculateCellValue={(rowData) => calculateAge(rowData.dateOfBirth)}
        />
        <Column
          dataField="primaryLanguage"
          caption="Language"
          dataType="string"
          allowSearch={false}
        />
        <Column dataField="reasonForReferral" caption="Reason for Referral" dataType="string" visible={false} />
        <Column
          dataField="parentName"
          caption="Parent Name"
          dataType="string"
        />
        <Column
          dataField="parentPhoneNumber"
          caption="Parent Phone Number"
          dataType="string"
        />
        <Column
          dataField="parentEmail"
          caption="Parent Email"
          dataType="string"
        />
        <Column
          dataField="fullAddress"
          caption="Full Address"
          dataType="string"
        />
        <Column
          dataField="referringPhysicianName"
          caption="Referring Physician Name"
          dataType="string"
          allowSearch={false}
        />
        <Column
          dataField="physicianPhoneNumber"
          caption="Physician Phone Number"
          dataType="string"
          allowSearch={false}
        />
        <Column
          dataField="physicianEmailAddress"
          caption="Physician Email Address"
          dataType="string"
          allowSearch={false}
        />
        <Column
          dataField="referralMethod"
          caption="Referral Method"
          dataType="string"
          allowSearch={false}
          cellRender={referralMethodRender}
        />
        <Column
          dataField="referringPhysicianId"
          caption="Referring Physician ID"
          dataType="number"
          allowSearch={false}
        />
        <Column
          dataField="authorizationsCount"
          caption="Authorizations"
          dataType="number"
          allowSearch={false}
        />
        <Column
          dataField="createdOn"
          caption="Created On"
          dataType="datetime"
          format="shortDateShortTime"
          allowSearch={false}
        />
        <Column
          dataField="statusLastUpdated"
          caption="Status Last Updated"
          dataType="datetime"
          format={"shortDateShortTime"}
          allowSearch={false}
        />
      </DataGrid>

      <Drawer
        opened={mobileRowClick != null}
        onClose={() => setMobileRowClick(null)}
        size="100%"
        styles={{
          content: { display: "flex", flexDirection: "column" },
          body: { flex: 1 },
        }}
      >
        <ChildInfo child={mobileRowClick || null} showAuthorizationsTab />
      </Drawer>
    </>
  );
};

export default ChildrenTable;
